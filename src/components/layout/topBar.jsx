import { useDispatch } from "react-redux";
import { <PERSON> } from "react-router-dom";
import { setToken } from "../../feature/slice/authSlice";
import UseThemeSettings from "../../hooks/useThemeSettings";
import { clearCountries } from "../../feature/slice/countriesSlice";
export default function TopBar() {
  const {
    toggleThemeDarkLight,
    toggleThemeDirection,
    toggleThemeLayout,
    toggleSidebarType,
  } = UseThemeSettings();

  const dispatch = useDispatch();
  const handleLogout = () => {
    dispatch(setToken(null));
    // Clear countries data
    dispatch(clearCountries());
  };

  return (
    <>
      {/* <!--  Header Start --> */}
      <header className="topbar">
        <div className="with-vertical">
          {/* <!-- ---------------------------------- -->
          <!-- Start Vertical Layout Header -->
          <!-- ---------------------------------- --> */}
          <nav className="navbar navbar-expand-lg p-0">
            <ul className="navbar-nav">
              <li className="nav-item d-flex d-xl-none">
                <Link
                  className="nav-link nav-icon-hover-bg rounded-circle  sidebartoggler "
                  id="headerCollapse"
                  onClick={toggleSidebarType}
                >
                  <iconify-icon
                    icon="solar:hamburger-menu-line-duotone"
                    class="fs-6"
                  ></iconify-icon>
                </Link>
              </li>
            </ul>

            <div className="d-block d-lg-none py-9 py-xl-0">
              <img
                src="/src/assets/images/logos/logo.svg"
                alt="matdash-img"
                style={{ width: "50%" }}
              />
            </div>
            <Link
              className="navbar-toggler p-0 border-0 nav-icon-hover-bg rounded-circle"
              href=""
              data-bs-toggle="collapse"
              data-bs-target="#navbarNav"
              aria-controls="navbarNav"
              aria-expanded="false"
              aria-label="Toggle navigation"
            >
              <iconify-icon
                icon="solar:menu-dots-bold-duotone"
                class="fs-6"
              ></iconify-icon>
            </Link>
            <div
              className="collapse navbar-collapse justify-content-end"
              id="navbarNav"
            >
              <div className="d-flex align-items-center justify-content-between">
                <ul className="navbar-nav flex-row mx-auto ms-lg-auto align-items-center justify-content-center">
                  {/* <!-- ------------------------------- -->
                  <!-- dark-layout light-layout Dropdown -->
                  <!-- ------------------------------- --> */}
                  <li className="nav-item">
                    <Link
                      className="nav-link moon dark-layout nav-icon-hover-bg rounded-circle"
                      onClick={toggleThemeDarkLight}
                    >
                      <iconify-icon
                        icon="solar:moon-line-duotone"
                        class="moon fs-6"
                      ></iconify-icon>
                    </Link>
                    <Link
                      className="nav-link sun light-layout nav-icon-hover-bg rounded-circle"
                      href=""
                      style={{ display: "none" }}
                    >
                      <iconify-icon
                        icon="solar:sun-2-line-duotone"
                        class="sun fs-6"
                      ></iconify-icon>
                    </Link>
                  </li>
                  {/* <!-- ------------------------------- -->
                  <!-- end dark-layout light-layout Dropdown -->
                  <!-- ------------------------------- --> */}
                  {/* <!-- ------------------------------- -->
                  <!-- start notification Dropdown -->
                  <!-- ------------------------------- --> */}
                  <li className="nav-item dropdown nav-icon-hover-bg rounded-circle">
                    <Link
                      className="nav-link position-relative"
                      href=""
                      id="drop2"
                      aria-expanded="false"
                    >
                      <iconify-icon
                        icon="solar:bell-bing-line-duotone"
                        class="fs-6"
                      ></iconify-icon>
                    </Link>
                    <div
                      className="dropdown-menu content-dd dropdown-menu-end dropdown-menu-animate-up"
                      aria-labelledby="drop2"
                    >
                      <div className="d-flex align-items-center justify-content-between py-3 px-7">
                        <h5 className="mb-0 fs-5 fw-semibold">Notifications</h5>
                        <span className="badge text-bg-primary rounded-4 px-3 py-1 lh-sm">
                          5 new
                        </span>
                      </div>
                      <div className="message-body" data-simplebar>
                        <Link
                          href=""
                          className="py-6 px-7 d-flex align-items-center dropdown-item gap-3"
                        >
                          <span className="flex-shrink-0 bg-danger-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-danger">
                            <iconify-icon icon="solar:widget-3-line-duotone"></iconify-icon>
                          </span>
                          <div className="w-75">
                            <div className="d-flex align-items-center justify-content-between">
                              <h6 className="mb-1 fw-semibold">Launch Admin</h6>
                              <span className="d-block fs-2">9:30 AM</span>
                            </div>
                            <span className="d-block text-truncate text-truncate fs-11">
                              Just see the my new admin!
                            </span>
                          </div>
                        </Link>
                        <Link
                          href=""
                          className="py-6 px-7 d-flex align-items-center dropdown-item gap-3"
                        >
                          <span className="flex-shrink-0 bg-primary-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-primary">
                            <iconify-icon icon="solar:calendar-line-duotone"></iconify-icon>
                          </span>
                          <div className="w-75">
                            <div className="d-flex align-items-center justify-content-between">
                              <h6 className="mb-1 fw-semibold">Event today</h6>
                              <span className="d-block fs-2">9:15 AM</span>
                            </div>
                            <span className="d-block text-truncate text-truncate fs-11">
                              Just a reminder that you have event
                            </span>
                          </div>
                        </Link>
                        <Link
                          href=""
                          className="py-6 px-7 d-flex align-items-center dropdown-item gap-3"
                        >
                          <span className="flex-shrink-0 bg-secondary-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-secondary">
                            <iconify-icon icon="solar:settings-line-duotone"></iconify-icon>
                          </span>
                          <div className="w-75">
                            <div className="d-flex align-items-center justify-content-between">
                              <h6 className="mb-1 fw-semibold">Settings</h6>
                              <span className="d-block fs-2">4:36 PM</span>
                            </div>
                            <span className="d-block text-truncate text-truncate fs-11">
                              You can customize this template as you want
                            </span>
                          </div>
                        </Link>
                        <Link
                          href=""
                          className="py-6 px-7 d-flex align-items-center dropdown-item gap-3"
                        >
                          <span className="flex-shrink-0 bg-warning-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-warning">
                            <iconify-icon icon="solar:widget-4-line-duotone"></iconify-icon>
                          </span>
                          <div className="w-75">
                            <div className="d-flex align-items-center justify-content-between">
                              <h6 className="mb-1 fw-semibold">Launch Admin</h6>
                              <span className="d-block fs-2">9:30 AM</span>
                            </div>
                            <span className="d-block text-truncate text-truncate fs-11">
                              Just see the my new admin!
                            </span>
                          </div>
                        </Link>
                        <Link
                          href=""
                          className="py-6 px-7 d-flex align-items-center dropdown-item gap-3"
                        >
                          <span className="flex-shrink-0 bg-primary-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-primary">
                            <iconify-icon icon="solar:calendar-line-duotone"></iconify-icon>
                          </span>
                          <div className="w-75">
                            <div className="d-flex align-items-center justify-content-between">
                              <h6 className="mb-1 fw-semibold">Event today</h6>
                              <span className="d-block fs-2">9:15 AM</span>
                            </div>
                            <span className="d-block text-truncate text-truncate fs-11">
                              Just a reminder that you have event
                            </span>
                          </div>
                        </Link>
                        <Link
                          href=""
                          className="py-6 px-7 d-flex align-items-center dropdown-item gap-3"
                        >
                          <span className="flex-shrink-0 bg-secondary-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-secondary">
                            <iconify-icon icon="solar:settings-line-duotone"></iconify-icon>
                          </span>
                          <div className="w-75">
                            <div className="d-flex align-items-center justify-content-between">
                              <h6 className="mb-1 fw-semibold">Settings</h6>
                              <span className="d-block fs-2">4:36 PM</span>
                            </div>
                            <span className="d-block text-truncate text-truncate fs-11">
                              You can customize this template as you want
                            </span>
                          </div>
                        </Link>
                      </div>
                      <div className="py-6 px-7 mb-1">
                        <button className="btn btn-primary w-100">
                          See All Notifications
                        </button>
                      </div>
                    </div>
                  </li>
                  {/* <!-- ------------------------------- -->
                  <!-- end notification Dropdown -->
                  <!-- ------------------------------- --> */}

                  {/* <!-- ------------------------------- -->
                  <!-- start language Dropdown -->
                  <!-- ------------------------------- --> */}
                  <li className="nav-item dropdown nav-icon-hover-bg rounded-circle">
                    <Link
                      className="nav-link"
                      href=""
                      id="drop2"
                      data-bs-toggle="dropdown"
                      aria-expanded="false"
                    >
                      <img
                        src="/src/assets/images/flag/icon-flag-en.svg"
                        alt="matdash-img"
                        width="20px"
                        height="20px"
                        className="rounded-circle object-fit-cover round-20"
                      />
                    </Link>
                    <div
                      className="dropdown-menu dropdown-menu-end dropdown-menu-animate-up"
                      aria-labelledby="drop2"
                    >
                      <div className="message-body">
                        <Link
                          onClick={toggleThemeLayout}
                          className="d-flex align-items-center gap-2 py-3 px-4 dropdown-item"
                        >
                          <div className="position-relative">
                            <img
                              src="/src/assets/images/flag/icon-flag-en.svg"
                              alt="matdash-img"
                              width="20px"
                              height="20px"
                              className="rounded-circle object-fit-cover round-20"
                            />
                          </div>
                          <p className="mb-0 fs-3">English (UK)</p>
                        </Link>

                        <Link
                          onClick={toggleThemeDirection}
                          className="d-flex align-items-center gap-2 py-3 px-4 dropdown-item"
                        >
                          <div className="position-relative">
                            <img
                              src="/src/assets/images/flag/icon-flag-sa.svg"
                              alt="matdash-img"
                              width="20px"
                              height="20px"
                              className="rounded-circle object-fit-cover round-20"
                            />
                          </div>
                          <p className="mb-0 fs-3">عربي (Arabic)</p>
                        </Link>
                      </div>
                    </div>
                  </li>
                  {/* <!-- ------------------------------- -->
                  <!-- end language Dropdown -->
                  <!-- ------------------------------- --> */}

                  {/* <!-- ------------------------------- -->
                  <!-- start profile Dropdown -->
                  <!-- ------------------------------- --> */}
                  <li className="nav-item dropdown">
                    <Link
                      className="nav-link"
                      href=""
                      id="drop1"
                      aria-expanded="false"
                    >
                      <div className="d-flex align-items-center gap-2 lh-base">
                        <img
                          src="/src/assets/images/profile/user-1.jpg"
                          className="rounded-circle"
                          width="35"
                          height="35"
                          alt="matdash-img"
                        />
                        <iconify-icon
                          icon="solar:alt-arrow-down-bold"
                          class="fs-2"
                        ></iconify-icon>
                      </div>
                    </Link>
                    <div
                      className="dropdown-menu profile-dropdown dropdown-menu-end dropdown-menu-animate-up"
                      aria-labelledby="drop1"
                    >
                      <div className="position-relative px-4 pt-3 pb-2">
                        <div className="d-flex align-items-center mb-3 pb-3 border-bottom gap-6">
                          <img
                            src="/src/assets/images/profile/user-1.jpg"
                            className="rounded-circle"
                            width="56"
                            height="56"
                            alt="matdash-img"
                          />
                          <div>
                            <h5 className="mb-0 fs-12">
                              David McMichael{" "}
                              <span className="text-success fs-11">Pro</span>
                            </h5>
                            <p className="mb-0 text-dark">
                              <EMAIL>
                            </p>
                          </div>
                        </div>
                        <div className="message-body">
                          <Link
                            to={"/account"}
                            className="p-2 dropdown-item h6 rounded-1"
                          >
                            My Profile
                          </Link>
                          <Link
                            to={"/"}
                            className="p-2 dropdown-item h6 rounded-1"
                          >
                            My Subscription
                          </Link>
                          <Link
                            to={"/"}
                            className="p-2 dropdown-item h6 rounded-1"
                          >
                            My Invoice{" "}
                            <span className="badge bg-danger-subtle text-danger rounded ms-8">
                              4
                            </span>
                          </Link>
                          <Link
                            to={"/"}
                            className="p-2 dropdown-item h6 rounded-1"
                          >
                            Account Settings
                          </Link>
                          <Link
                            onClick={handleLogout}
                            className="p-2 dropdown-item h6 rounded-1"
                          >
                            Sign Out
                          </Link>
                        </div>
                      </div>
                    </div>
                  </li>
                  {/* <!-- ------------------------------- -->
                  <!-- end profile Dropdown -->
                  <!-- ------------------------------- --> */}
                </ul>
              </div>
            </div>
          </nav>
          {/* <!-- ---------------------------------- -->
          <!-- End Vertical Layout Header -->
          <!-- ---------------------------------- --> */}
        </div>
        <div className="app-header with-horizontal">
          <nav className="navbar navbar-expand-xl container-fluid p-0">
            <ul className="navbar-nav align-items-center">
              <li className="nav-item d-flex d-xl-none">
                <Link
                  className="nav-link sidebartoggler nav-icon-hover-bg rounded-circle"
                  id="sidebarCollapse"
                  href=""
                >
                  <iconify-icon
                    icon="solar:hamburger-menu-line-duotone"
                    class="fs-7"
                  ></iconify-icon>
                </Link>
              </li>
              <li className="nav-item d-none d-xl-flex align-items-center">
                <Link to={"/"} className="text-nowrap nav-link">
                  <img
                    src="/src/assets/images/logos/logo.svg"
                    alt="matdash-img"
                  />
                </Link>
              </li>
            </ul>
            <div className="d-block d-xl-none">
              <Link to={"/"} className="text-nowrap nav-link">
                <img
                  src="/src/assets/images/logos/logo.svg"
                  alt="matdash-img"
                />
              </Link>
            </div>
            <Link
              className="navbar-toggler nav-icon-hover p-0 border-0 nav-icon-hover-bg rounded-circle"
              href=""
              data-bs-toggle="collapse"
              data-bs-target="#navbarNav"
              aria-controls="navbarNav"
              aria-expanded="false"
              aria-label="Toggle navigation"
            >
              <span className="p-2">
                <i className="ti ti-dots fs-7"></i>
              </span>
            </Link>
            <div
              className="collapse navbar-collapse justify-content-end"
              id="navbarNav"
            >
              <div className="d-flex align-items-center justify-content-between px-0 px-xl-8">
                <ul className="navbar-nav flex-row mx-auto ms-lg-auto align-items-center justify-content-center">
                  <li className="nav-item">
                    <Link
                      className="nav-link nav-icon-hover-bg rounded-circle moon dark-layout"
                      onClick={toggleThemeDarkLight}
                    >
                      <iconify-icon
                        icon="solar:moon-line-duotone"
                        class="moon fs-6"
                      ></iconify-icon>
                    </Link>
                    <Link
                      className="nav-link nav-icon-hover-bg rounded-circle sun light-layout"
                      onClick={toggleThemeDarkLight}
                      style={{ display: "none" }}
                    >
                      <iconify-icon
                        icon="solar:sun-2-line-duotone"
                        class="sun fs-6"
                      ></iconify-icon>
                    </Link>
                  </li>
                  {/* <!-- ------------------------------- -->
                  <!-- start notification Dropdown -->
                  <!-- ------------------------------- --> */}
                  <li className="nav-item dropdown nav-icon-hover-bg rounded-circle">
                    <Link
                      className="nav-link position-relative"
                      href=""
                      id="drop2"
                      aria-expanded="false"
                    >
                      <iconify-icon
                        icon="solar:bell-bing-line-duotone"
                        class="fs-6"
                      ></iconify-icon>
                    </Link>
                    <div
                      className="dropdown-menu content-dd dropdown-menu-end dropdown-menu-animate-up"
                      aria-labelledby="drop2"
                    >
                      <div className="d-flex align-items-center justify-content-between py-3 px-7">
                        <h5 className="mb-0 fs-5 fw-semibold">Notifications</h5>
                        <span className="badge text-bg-primary rounded-4 px-3 py-1 lh-sm">
                          5 new
                        </span>
                      </div>
                      <div className="message-body" data-simplebar>
                        <Link
                          href=""
                          className="py-6 px-7 d-flex align-items-center dropdown-item gap-3"
                        >
                          <span className="flex-shrink-0 bg-danger-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-danger">
                            <iconify-icon icon="solar:widget-3-line-duotone"></iconify-icon>
                          </span>
                          <div className="w-75">
                            <div className="d-flex align-items-center justify-content-between">
                              <h6 className="mb-1 fw-semibold">Launch Admin</h6>
                              <span className="d-block fs-2">9:30 AM</span>
                            </div>
                            <span className="d-block text-truncate text-truncate fs-11">
                              Just see the my new admin!
                            </span>
                          </div>
                        </Link>
                        <Link
                          href=""
                          className="py-6 px-7 d-flex align-items-center dropdown-item gap-3"
                        >
                          <span className="flex-shrink-0 bg-primary-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-primary">
                            <iconify-icon icon="solar:calendar-line-duotone"></iconify-icon>
                          </span>
                          <div className="w-75">
                            <div className="d-flex align-items-center justify-content-between">
                              <h6 className="mb-1 fw-semibold">Event today</h6>
                              <span className="d-block fs-2">9:15 AM</span>
                            </div>
                            <span className="d-block text-truncate text-truncate fs-11">
                              Just a reminder that you have event
                            </span>
                          </div>
                        </Link>
                        <Link
                          href=""
                          className="py-6 px-7 d-flex align-items-center dropdown-item gap-3"
                        >
                          <span className="flex-shrink-0 bg-secondary-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-secondary">
                            <iconify-icon icon="solar:settings-line-duotone"></iconify-icon>
                          </span>
                          <div className="w-75">
                            <div className="d-flex align-items-center justify-content-between">
                              <h6 className="mb-1 fw-semibold">Settings</h6>
                              <span className="d-block fs-2">4:36 PM</span>
                            </div>
                            <span className="d-block text-truncate text-truncate fs-11">
                              You can customize this template as you want
                            </span>
                          </div>
                        </Link>
                        <Link
                          href=""
                          className="py-6 px-7 d-flex align-items-center dropdown-item gap-3"
                        >
                          <span className="flex-shrink-0 bg-warning-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-warning">
                            <iconify-icon icon="solar:widget-4-line-duotone"></iconify-icon>
                          </span>
                          <div className="w-75">
                            <div className="d-flex align-items-center justify-content-between">
                              <h6 className="mb-1 fw-semibold">Launch Admin</h6>
                              <span className="d-block fs-2">9:30 AM</span>
                            </div>
                            <span className="d-block text-truncate text-truncate fs-11">
                              Just see the my new admin!
                            </span>
                          </div>
                        </Link>
                        <Link
                          href=""
                          className="py-6 px-7 d-flex align-items-center dropdown-item gap-3"
                        >
                          <span className="flex-shrink-0 bg-primary-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-primary">
                            <iconify-icon icon="solar:calendar-line-duotone"></iconify-icon>
                          </span>
                          <div className="w-75">
                            <div className="d-flex align-items-center justify-content-between">
                              <h6 className="mb-1 fw-semibold">Event today</h6>
                              <span className="d-block fs-2">9:15 AM</span>
                            </div>
                            <span className="d-block text-truncate text-truncate fs-11">
                              Just a reminder that you have event
                            </span>
                          </div>
                        </Link>
                        <Link
                          href=""
                          className="py-6 px-7 d-flex align-items-center dropdown-item gap-3"
                        >
                          <span className="flex-shrink-0 bg-secondary-subtle rounded-circle round d-flex align-items-center justify-content-center fs-6 text-secondary">
                            <iconify-icon icon="solar:settings-line-duotone"></iconify-icon>
                          </span>
                          <div className="w-75">
                            <div className="d-flex align-items-center justify-content-between">
                              <h6 className="mb-1 fw-semibold">Settings</h6>
                              <span className="d-block fs-2">4:36 PM</span>
                            </div>
                            <span className="d-block text-truncate text-truncate fs-11">
                              You can customize this template as you want
                            </span>
                          </div>
                        </Link>
                      </div>
                      <div className="py-6 px-7 mb-1">
                        <button className="btn btn-primary w-100">
                          See All Notifications
                        </button>
                      </div>
                    </div>
                  </li>
                  {/* <!-- ------------------------------- -->
                  <!-- end notification Dropdown -->
                  <!-- ------------------------------- -->

                  <!-- ------------------------------- -->
                  <!-- start language Dropdown -->
                  <!-- ------------------------------- --> */}
                  <li className="nav-item dropdown nav-icon-hover-bg rounded-circle">
                    <Link
                      className="nav-link"
                      href=""
                      id="drop2"
                      data-bs-toggle="dropdown"
                      aria-expanded="false"
                    >
                      <img
                        src="/src/assets/images/flag/icon-flag-en.svg"
                        alt="matdash-img"
                        width="20px"
                        height="20px"
                        className="rounded-circle object-fit-cover round-20"
                      />
                    </Link>
                    <div
                      className="dropdown-menu dropdown-menu-end dropdown-menu-animate-up"
                      aria-labelledby="drop2"
                    >
                      <div className="message-body">
                        <Link
                          onClick={toggleThemeLayout}
                          className="d-flex align-items-center gap-2 py-3 px-4 dropdown-item"
                        >
                          <div className="position-relative">
                            <img
                              src="/src/assets/images/flag/icon-flag-en.svg"
                              alt="matdash-img"
                              width="20px"
                              height="20px"
                              className="rounded-circle object-fit-cover round-20"
                            />
                          </div>
                          <p className="mb-0 fs-3">English (UK)</p>
                        </Link>
                        <Link
                          onClick={toggleThemeDirection}
                          className="d-flex align-items-center gap-2 py-3 px-4 dropdown-item"
                        >
                          <div className="position-relative">
                            <img
                              src="/src/assets/images/flag/icon-flag-sa.svg"
                              alt="matdash-img"
                              width="20px"
                              height="20px"
                              className="rounded-circle object-fit-cover round-20"
                            />
                          </div>
                          <p className="mb-0 fs-3">عربي (Arabic)</p>
                        </Link>
                      </div>
                    </div>
                  </li>
                  {/* <!-- ------------------------------- -->
                  <!-- end language Dropdown -->
                  <!-- ------------------------------- -->

                  <!-- ------------------------------- -->
                  <!-- start profile Dropdown -->
                  <!-- ------------------------------- --> */}
                  <li className="nav-item dropdown">
                    <Link
                      className="nav-link"
                      href=""
                      id="drop1"
                      aria-expanded="false"
                    >
                      <div className="d-flex align-items-center gap-2 lh-base">
                        <img
                          src="/src/assets/images/profile/user-1.jpg"
                          className="rounded-circle"
                          width="35"
                          height="35"
                          alt="matdash-img"
                        />
                        <iconify-icon
                          icon="solar:alt-arrow-down-bold"
                          class="fs-2"
                        ></iconify-icon>
                      </div>
                    </Link>
                    <div
                      className="dropdown-menu profile-dropdown dropdown-menu-end dropdown-menu-animate-up"
                      aria-labelledby="drop1"
                    >
                      <div className="position-relative px-4 pt-3 pb-2">
                        <div className="d-flex align-items-center mb-3 pb-3 border-bottom gap-6">
                          <img
                            src="/src/assets/images/profile/user-1.jpg"
                            className="rounded-circle"
                            width="56"
                            height="56"
                            alt="matdash-img"
                          />
                          <div>
                            <h5 className="mb-0 fs-12">
                              David McMichael{" "}
                              <span className="text-success fs-11">Pro</span>
                            </h5>
                            <p className="mb-0 text-dark">
                              <EMAIL>
                            </p>
                          </div>
                        </div>
                        <div className="message-body">
                          <Link
                            to={"/"}
                            className="p-2 dropdown-item h6 rounded-1"
                          >
                            My Profile
                          </Link>
                          <Link
                            to={"/"}
                            className="p-2 dropdown-item h6 rounded-1"
                          >
                            My Subscription
                          </Link>
                          <Link
                            to={"/"}
                            className="p-2 dropdown-item h6 rounded-1"
                          >
                            My Invoice{" "}
                            <span className="badge bg-danger-subtle text-danger rounded ms-8">
                              4
                            </span>
                          </Link>
                          <Link
                            to={"/"}
                            className="p-2 dropdown-item h6 rounded-1"
                          >
                            Account Settings
                          </Link>
                          <Link
                            onClick={handleLogout}
                            className="p-2 dropdown-item h6 rounded-1"
                          >
                            Sign Out
                          </Link>
                        </div>
                      </div>
                    </div>
                  </li>
                  {/* <!-- ------------------------------- -->
              <!-- end profile Dropdown -->
              <!-- ------------------------------- --> */}
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </header>
      {/* <!--  Header End --> */}
    </>
  );
}
