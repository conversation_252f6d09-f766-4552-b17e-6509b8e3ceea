import { useNavigate } from "react-router-dom";
// import { Link } from "react-router-dom";
import { useLoginMutation } from "../../feature/api/authApiSlice";
import { handleApiErrors } from "../../hooks/handleApiErrors";

import { Formik, Form } from "formik";
import * as yup from "yup";
import FormikField from "../../components/formikField";
import { useDispatch } from "react-redux";
import { setToken, setUserType } from "../../feature/slice/authSlice";
import WebLoader from "../../components/webLoader";
import { setMenuModule } from "../../feature/slice/appConfigSlice";
const initValues = {
  email: "",
  password: "",
};

const validation = yup.object().shape({
  email: yup.string().email().required().label("Email Address"),
  password: yup.string().required("Password is required!").label("Password"),
});
export default function Home() {
  const [handleLoginApi, { isLoading }] = useLoginMutation();
  const navigate = useNavigate(); // Initialize the navigation hook
  const dispatch = useDispatch(); // Initialize the dispatch hook
  const handleSubmit = async (body) => {
    try {
      const resp = await handleLoginApi(body).unwrap();
      if (resp?.data?.access_token != "") {
        const accessToken = resp.data.access_token;
        dispatch(setToken(accessToken));
        dispatch(setUserType(resp.data.user.account_type));
        dispatch(setMenuModule("mini-1"));
        navigate("/dashboard"); // Redirect to the desired page
      }
    } catch (error) {
      handleApiErrors(error);
    }
  };
  if (isLoading) return <WebLoader />;
  return (
    <>
      <div className="position-relative overflow-hidden radial-gradient min-vh-100 w-100">
        <div className="position-relative z-index-5">
          <div className="row gx-0">
            <div className="col-lg-6 col-xl-5 col-xxl-4">
              <div className="min-vh-100 bg-body row justify-content-center align-items-center p-5">
                <div className="col-12 auth-card">
                  <a href="#" className="text-nowrap logo-img d-block w-100">
                    <img
                      src="/src/assets/images/logos/logo.svg"
                      className="dark-logo"
                      alt="Logo-Dark"
                    />
                  </a>
                  <h2 className="mb-2 mt-4 fs-7 fw-bolder">Sign In</h2>
                  <p className="mb-9">Lets get you signed in</p>
                  <Formik
                    initialValues={initValues}
                    validationSchema={validation}
                    onSubmit={handleSubmit}
                  >
                    <Form
                      name="login-form"
                      className="needs-validation"
                      autoComplete="off"
                    >
                      <div className="mb-3">
                        <label
                          htmlFor="exampleInputEmail1"
                          className="form-label"
                        >
                          Email Address
                        </label>

                        <FormikField
                          name="email"
                          type="text"
                          className="form-control"
                          id="email"
                          placeholder="Enter your email *"
                          aria-describedby="emailHelp"
                          autoComplete="off"
                        />
                      </div>
                      <div className="mb-4">
                        <label
                          htmlFor="exampleInputPassword1"
                          className="form-label"
                        >
                          Password
                        </label>
                        <FormikField
                          name="password"
                          type="password"
                          className="form-control"
                          id="password"
                          placeholder="Enter your password *"
                          autoComplete="off"
                        />
                      </div>
                      <div className="d-sm-flex align-items-center justify-content-between mb-4">
                        <div className="form-check">
                          <input
                            className="form-check-input primary"
                            type="checkbox"
                            value=""
                            id="flexCheckChecked"
                          />
                          <label
                            className="form-check-label text-dark"
                            htmlFor="flexCheckChecked"
                          >
                            Remeber this Device
                          </label>
                        </div>
                        <a className="text-primary fw-medium" href="#">
                          Forgot Password ?
                        </a>
                      </div>
                      <button
                        id="pos-top-right"
                        className="btn btn-primary w-100 py-8 mb-4 rounded-1"
                        type="submit"
                      >
                        Sign In
                      </button>
                    </Form>
                  </Formik>
                </div>
              </div>
            </div>

            <div className="col-lg-6 col-xl-7 col-xxl-8 position-relative overflow-hidden bg-dark d-none d-lg-block">
              <div className="circle-top"></div>
              <div>
                <img
                  src="/src/assets/images/logos/favicon.png"
                  className="circle-bottom"
                  alt="Logo-Dark"
                />
              </div>
              <div className="d-lg-flex align-items-center z-index-5 position-relative h-n80">
                <div className="row justify-content-center w-100">
                  <div className="col-lg-6">
                    <h2 className="text-white fs-10 mb-3 lh-sm">
                      Welcome to
                      <br />
                      Technology Lab ERP
                    </h2>
                    <span className="opacity-75 fs-4 text-white d-block mb-3">
                      Technology Lab ERP helps developers to build organized and
                      well
                      <br />
                      coded dashboards full of beautiful and rich modules.
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
