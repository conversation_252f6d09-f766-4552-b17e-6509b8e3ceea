import CommonHeader from "../../components/layout/common_header";
import CommonFooter from "../../components/layout/common_footer";
import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import { Link, useNavigate } from "react-router-dom";
import {
  useDeleteInventoryMutation,
  useGetInventoryListQuery,
} from "../../feature/api/inventoryDataApiSlice";
import { Table } from "../../components/datatable";
import { handleApiErrors } from "../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../hooks/handleApiSucess"
import { useMemo, useState } from "react";
import WebLoader from "../../components/webLoader";
import { PaginationComponent } from "../../components/pagination";
import { useGetProductListQuery } from "../../feature/api/productDataApiSlice";
import { useGetShopBranchsQuery } from "../../feature/api/branchDataApiSlice";
import { useGetGeneralStatusQuery } from "../../feature/api/statusApiSlice";
import useConfirmDelete from "../../hooks/useConfirmDelete";

export default function Inventory() {
  const activePage = "Inventory List";
  const linkHref = "/dashboard";

  const navigation = useNavigate();
  /* **************** Start list all Inventory ******************* */
  const [currentPage, setCurrentPage] = useState(1);
  const [filterProductId, setFilterProductId] = useState(null);
  const [filterBranchId, setFilterBranchId] = useState(null);
  const [filterStockStatus, setFilterStockStatus] = useState(null);
  const [filterStatus, setFilterStatus] = useState("");
  const [filterKeywords, setFilterKeywords] = useState("");
  const { data: inventoryListResp, isLoading } = useGetInventoryListQuery({
    page: currentPage,
    stock_status: parseInt(filterStockStatus),
    product_id: parseInt(filterProductId),
    branch_id: parseInt(filterBranchId),
    status: parseInt(filterStatus),
    keywords: filterKeywords,
  });
  const inventoryList = useMemo(() => {
    if (!inventoryListResp?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return inventoryListResp?.data?.list;
  }, [currentPage, inventoryListResp?.data.list]);
  const pageData = useMemo(
    () => inventoryListResp?.data?.page ?? null,
    [inventoryListResp]
  );

  /* **************** End list all Inventory ******************* */

  /* ****************  Start Filter ****************** */
   const handleStatusFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterStatus(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleStockStatusFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterStockStatus(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleProductFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterProductId(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleBranchFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterBranchId(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleKeywordsFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterKeywords(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };

    /* **************** Start list Stock Status ******************* */
    const stockStatusList = [
      { value: 1,
        label: "In Stock",
      },
      {
        value: 2,
        label: "Out of Stock"
      }
    ];
    /* **************** End list Stock Status ******************* */

   /* **************** Start Branch List ******************* */
   const branchListResp = useGetShopBranchsQuery();
   const branchList = branchListResp.data?.data || [];
   const branchesList = branchList.map((values) => ({
     value: values.id,
     label: values.branch_name + ' (' + values.branch_type_name + ')',
   }));
   /* **************** End Branch List ******************* */

  /* **************** Start list General Status ******************* */
  const generalStatuData = useGetGeneralStatusQuery();
  const generalStatuDataList = generalStatuData?.data?.data || [];
  const generalStatusList = generalStatuDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** Start list General Status ******************* */
  
   /* **************** Start Product List ******************* */
  const { data: productListResp } = useGetProductListQuery({
    page: 1,
  });

  const productList = productListResp?.data?.list || [];
  const productAry = productList.map((product) => ({
    value: product.id,
    label: product.title,
  }));
  /* **************** End Product List ******************* */

  /* **************** End Filter ***************** */

  /* **************** Start Paginatation ***************** */
  const fetchData = async (page) => {
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Paginatation ***************** */

  /* **************** Start Delete Inventory ***************** */
   const { showSimpleConfirm } = useConfirmDelete({
            title: 'Delete Inventory?',
            text: 'Are you sure you want to delete this inventory?',
            confirmButtonText: 'Yes, delete inventory!',
            cancelButtonText: 'Cancel'
          });
  const [handledeleteInventoryApi, { isLoading: isDeleteLoading }] =
  useDeleteInventoryMutation();
  const onDeleteInventoryHandler = async (id) => {
    const confirmed = await showSimpleConfirm();
      if (confirmed) {
        try {
          const body = { id: id };
          const resp = await handledeleteInventoryApi(body).unwrap();
          handleApiSuccess(resp);
          navigation("/inventory"); // Redirect to the desired page
        } catch (error) {
          handleApiErrors(error);
        }
    }
  };

  /* **************** End Delete Inventory ***************** */
  
  const onEditInventoryDetailsHandler = (d) => {
    navigation("/editInventory", { state: d });
  };

    /* **************** Web Loader  ******************* */
    if (isLoading || isDeleteLoading)
      return <WebLoader />;
    /* **************** End Web Loader  ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">Inventory List</h4>
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <Link
                          type="button"
                          to={"/createInventory"}
                          className="btn btn-primary"
                        >
                          Add Inventory
                        </Link>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                    <div className="d-flex  gap-6">
                      <div>
                        <select
                          value={filterProductId}
                          className="form-control search-chat py-2 "
                          onChange={handleProductFilter}
                        >
                          <option value="">All Products</option>
                          {productAry.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterBranchId}
                          className="form-control search-chat py-2 "
                          onChange={handleBranchFilter}
                        >
                          <option value="">All Branches</option>
                          {branchesList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterStockStatus}
                          className="form-control search-chat py-2 "
                          onChange={handleStockStatusFilter}
                        >
                          <option value="">All Stock Status</option>
                          {stockStatusList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterStatus}
                          className="form-control search-chat py-2 "
                          onChange={handleStatusFilter}
                        >
                          <option value="">All Status</option>
                          {generalStatusList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div className="position-relative">
                      <input
                        type="text"
                        className="form-control search-chat py-2 ps-5"
                        id="text-srh"
                        onChange={handleKeywordsFilter}
                        placeholder="Keyword Search..."
                        value={filterKeywords}
                      />
                      <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                    </div>
                  </div>
                  <Table
                    headCells={[
                      { key: "sel_id", label: "#", align: "left" },
                      {
                        key: "branch_name",
                        label: "Branch",
                        align: "left",
                      },
                      {
                        key: "product_name",
                        label: "Product",
                        align: "left",
                      },
                      {
                        key: "bar_code",
                        label: "Barcode",
                        align: "left",
                      },
                      {
                        key: "selling_price",
                        label: "Price",
                        align: "left",
                      },
                      {
                        key: "stock_quantity",
                        label: "Stock Quantity",
                        align: "left",
                      },
                      {
                        key: "stock_status_text",
                        key_id: "stock_status",
                        label: "Stock",
                        align: "left",
                      },
                      {
                        key: "status_name",
                        key_id: "status",
                        label: "Status",
                        align: "left",
                      },
                    ]}
                    data={inventoryList}
                    onDeleteHandler={onDeleteInventoryHandler}
                    onEditHandler={onEditInventoryDetailsHandler}
                  />
                  <PaginationComponent
                  totalCount={pageData?.total_count}
                  pageSize={pageData?.page_size}
                  currentPage={currentPage}
                  setCurrentPage={setCurrentPage}
                  onPageChange={fetchData}
                />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}
