import { Form, useLocation, useNavigate } from "react-router-dom";
import CommonHeader from "../../../components/layout/common_header";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { Formik } from "formik";
import FormikField from "../../../components/formikField";
import * as yup from "yup";
import MapComponent from "../../../components/map_component";
import CommonFooter from "../../../components/layout/common_footer";
import {
  useGetbranchTypesQuery,
  useUpdateShopBranchMutation,
} from "../../../feature/api/branchDataApiSlice";
import { useEffect, useMemo, useState } from "react";
import { handleCustomError } from "../../../hooks/handleCustomError";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { useGetBranchStatusQuery } from "../../../feature/api/statusApiSlice";
import WebLoader from "../../../components/webLoader";
import { useDispatch, useSelector } from "react-redux";
import { useGetCountriesQuery } from "../../../feature/api/countriesDataApiSlice";
import { setCountries } from "../../../feature/slice/countriesSlice";

const initialValues = {
  branch_type: "",
  branch_name: "",
  branch_name_ar: "",
  branch_phone_code: "",
  branch_phone: "",
  alternate_branch_phone_code: "",
  alternate_branch_phone: "",
  branch_email: "",
  branch_location: "",
  branch_location_ar: "",
  branch_address: "",
  branch_address_ar: "",
  status: "",
};

const validation = yup.object().shape({
  branch_type: yup.string().required().label("Branch Type"),
  branch_name: yup.string().required().label("Branch Name in English"),
  branch_name_ar: yup.string().required().label("Branch Name in Arabic"),
  branch_phone_code: yup.string().required().label("Phone Number"),
  branch_phone: yup
    .string()
    .matches(/^\d{8}$/, "Phone number must be exactly 8 digits")
    .required()
    .label("Phone Number"),
  branch_email: yup.string().email().required().label("Email Address"),
  branch_location: yup.string().required().label("Branch location in English"),
  branch_location_ar: yup
    .string()
    .required()
    .label("Branch location in Arabic"),
  branch_address: yup.string().required().label("Branch address in English"),
  branch_address_ar: yup.string().required().label("Branch address in Arabic"),
  status: yup.string().required().label("Branch Status"),
});

const EditBranch = () => {
  const { state } = useLocation();
  const branchData = state;
  const navigation = useNavigate();
  const dispatch = useDispatch();
  const accessToken = useSelector((state) => state.authState.token);
  const { data: countriesData, isLoading: isCountriesLoading, error } = useGetCountriesQuery(undefined, { skip: !accessToken });

  useEffect(() => {
    if (countriesData) {
      dispatch(setCountries(countriesData));
    }
    if (error) {
      console.error("Failed to fetch countries:", error);
    }
  }, [countriesData, error, dispatch]);

  const countries = useSelector((state) => state.countriesState.countries);
  const countriesList = countries?.data
    ? countries.data.map((values) => ({
        value: values.phone_code,
        label: values.name + ' (' + values.phone_code + ')',
      }))
    : [];

  if (!branchData) {
    handleCustomError("Error was found, please try again later!");
    navigation("/branch_list");
  }

  const activePage = "Branches Master";
  const linkHref = "/dashboard";
  // get Branch type
  const branchTypesResp = useGetbranchTypesQuery();
  const branchTypes = branchTypesResp.data?.data || [];
  const branchTypesAry = branchTypes.map((type) => ({
    value: type.id,
    label: type.branch_type,
  }));

  const branchStatusResp = useGetBranchStatusQuery();
  const branchStatus = branchStatusResp.data?.data || [];
  const branchStatusAry = branchStatus.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  const [locations, setLocation] = useState({
    lat: 25.4090082,
    lng: 51.5042926,
  });
  useEffect(() => {
    setLocation({
      lat: parseFloat(branchData?.latitude) || 25.4090082,
      lng: parseFloat(branchData?.longitude) || 51.5042926,
    });
  }, [branchData]);

  const EditBranchValues = useMemo(
    () =>
      !branchData
        ? initialValues
        : {
            branch_type: branchData?.branch_type || "",
            branch_name: branchData?.branch_name || "",
            branch_name_ar: branchData?.branch_name_ar || "",
            branch_phone_code: branchData?.branch_phone_code || "",
            branch_phone: branchData?.branch_phone || "",
            alternate_branch_phone_code:
              branchData?.alternate_branch_phone_code || "",
            alternate_branch_phone: branchData?.alternate_branch_phone || "",
            branch_email: branchData?.branch_email || "",
            branch_location: branchData?.branch_location || "",
            branch_location_ar: branchData?.branch_location_ar || "",
            branch_address: branchData?.branch_address || "",
            branch_address_ar: branchData?.branch_address_ar || "",
            status: branchData?.status || "",
          },
    [branchData]
  );
  const [handleUpdateBranchApi, { isLoading: isLoading }] = useUpdateShopBranchMutation();
  const formSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        branch_id: parseInt(branchData?.id),
        branch_type: parseInt(body.branch_type), // Convert branch_type to an integer
        latitude: String(locations.lat),
        longitude: String(locations.lng),
        status: parseInt(body.status),
      };
      const resp = await handleUpdateBranchApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      navigation("/branch_list"); // Redirect to the desired page
    } catch (error) {
      handleApiErrors(error);
    }
  };

   /* **************** Start Web Loader  ******************* */
          if (isLoading || isCountriesLoading)
            return <WebLoader />;
    /* **************** End Web Loader  ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">Edit Branch Details</h4>
                            <p className="card-subtitle mb-4">
                              To update Branch Details, edit details and save
                              from here
                            </p>
                            <Formik
                              initialValues={EditBranchValues}
                              enableReinitialize
                              validationSchema={validation}
                              onSubmit={formSubmit}
                            >
                              {({ handleSubmit }) => (
                                <Form
                                  name="branch-update"
                                  className="needs-validation"
                                  autoComplete="off"
                                >
                                  <div className="row">
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="branch_type"
                                          className="form-label"
                                        >
                                          Branch Type
                                        </label>
                                        <FormikField
                                          name="branch_type"
                                          id="branch_type"
                                          className="form-select"
                                          type="select"
                                          options={branchTypesAry}
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="branch_name"
                                          className="form-label"
                                        >
                                          Branch Name in English
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="branch_name"
                                          id="branch_name"
                                          placeholder="Branch Name  in English *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="branch_name_ar"
                                          className="form-label"
                                        >
                                          Branch Name in Arabic
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="branch_name_ar"
                                          id="branch_name_ar"
                                          placeholder="Branch Name  in Arabic *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label className="form-label">
                                          Branch Phone Number
                                        </label>
                                        <div className="row">
                                          <div className="col-4">
                                            <FormikField
                                              name="branch_phone_code"
                                              id="branch_phone_code"
                                              className="form-select"
                                              type="select"
                                              options={countriesList}
                                            />
                                          </div>

                                          <div className="col-8">
                                            <FormikField
                                              type="number"
                                              name="branch_phone"
                                              id="branch_phone"
                                              placeholder="Branch Phone Number *"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label className="form-label">
                                          Alternate Branch Phone Number
                                        </label>
                                        <div className="row">
                                          <div className="col-4">
                                            <FormikField
                                              name="alternate_branch_phone_code"
                                              id="alternate_branch_phone_code"
                                              className="form-select"
                                              type="select"
                                              options={countriesList}
                                            />
                                          </div>
                                          <div className="col-8">
                                            <FormikField
                                              type="number"
                                              name="alternate_branch_phone"
                                              id="alternate_branch_phone"
                                              placeholder="Alternate  Branch Phone Number"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="branch_email"
                                          className="form-label"
                                        >
                                          Branch Email Address
                                        </label>
                                        <FormikField
                                          type="email"
                                          name="branch_email"
                                          id="branch_email"
                                          placeholder="Branch Email Address *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="branch_location"
                                          className="form-label"
                                        >
                                          Branch Location in English
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="branch_location"
                                          id="branch_location"
                                          placeholder="Branch Location  in English  *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="branch_location_ar"
                                          className="form-label"
                                        >
                                          Branch Location in Arabic
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="branch_location_ar"
                                          id="branch_location_ar"
                                          placeholder="Branch Location  in Arabic  *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="branch_address"
                                          className="form-label"
                                        >
                                          Branch Address in English
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="branch_address"
                                          id="branch_address"
                                          placeholder="Branch Address  in English  *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="branch_address_ar"
                                          className="form-label"
                                        >
                                          Branch Address in Arabic
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="branch_address_ar"
                                          id="branch_address_ar"
                                          placeholder="Branch Address  in Arabic  *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="branch_map"
                                          className="form-label"
                                        >
                                          Choose Branch Location in Google Map
                                        </label>
                                        <MapComponent
                                          location={locations}
                                          setLocation={setLocation}
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="status"
                                          className="form-label"
                                        >
                                          Branch Status
                                        </label>
                                        <FormikField
                                          name="status"
                                          id="status"
                                          className="form-select"
                                          type="select"
                                          options={branchStatusAry}
                                        />
                                      </div>
                                    </div>
                                    <div className="col-12">
                                      <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                        <button
                                          className="btn btn-primary"
                                          onClick={handleSubmit}
                                        >
                                          Update Branch Details
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                </Form>
                              )}
                            </Formik>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
};

export default EditBranch;
