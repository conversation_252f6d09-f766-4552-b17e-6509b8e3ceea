import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  countries: [],
};

const countriesSlice = createSlice({
  name: "countriesState",
  initialState,
  reducers: {
    setCountries(state, action) {
      state.countries = action?.payload || [];
    },
    clearCountries(state) {
      state.countries = [];
    },
  },
});

export const { setCountries, clearCountries } = countriesSlice.actions;
export default countriesSlice.reducer;
