import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";

import { persistReducer, persistStore } from "redux-persist";

import storage from "redux-persist/lib/storage";
// api - slice
import { authApiSlice } from "./api/authApiSlice";
import { shopDataApiSlice } from "./api/shopDataApiSlice";
import { branchDataApiSlice } from "./api/branchDataApiSlice";
import { statusApiSlice } from "./api/statusApiSlice";
import { rolesDataApiSlice } from "./api/rolesDataApiSlice";
import { staffsDataApiSlice } from "./api/staffsDataApiSlice";
import { rootReducer } from "./rootReducer";
import { categoriesDataApiSlice } from "./api/categoriesDataApiSlice";
import { subCategoriesDataApiSlice } from "./api/subCategoriesDataApiSlice";
import { childCategoriesDataApiSlice } from "./api/childCategoriesDataApiSlice";
import { attributesDataApiSlice } from "./api/attributesDataApiSlice";
import { attributesValuesDataApiSlice } from "./api/attributesValuesDataApiSlice";
import { brandsDataApiSlice } from "./api/brandsDataApiSlice";
import { productDataApiSlice } from "./api/productDataApiSlice";
import { inventoryDataApiSlice } from "./api/inventoryDataApiSlice";
import { dashboardDataApiSlice } from "./api/dashboardDataApiSlice";
import { customerDataApiSlice } from "./api/customerDataApiSlice";
import { customerContactsDataApiSlice } from "./api/customerContactsDataApiSlice";
import { customerNotesDataApiSlice } from "./api/customerNotesDataApiSlice";
import { customerAttachmentsDataApiSlice } from "./api/customerAttachmentsDataApiSlice";
import { supplierDataApiSlice } from "./api/supplierDataApiSlice";
import { countriesDataApiSlice } from "./api/countriesDataApiSlice";

const persistConfig = {
  key: "root",
  storage,
  whitelist: ["authState", "appConfig"],
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({ serializableCheck: false }).concat(
      authApiSlice.middleware,
      shopDataApiSlice.middleware,
      branchDataApiSlice.middleware,
      statusApiSlice.middleware,
      rolesDataApiSlice.middleware,
      staffsDataApiSlice.middleware,
      categoriesDataApiSlice.middleware,
      subCategoriesDataApiSlice.middleware,
      childCategoriesDataApiSlice.middleware,
      attributesDataApiSlice.middleware,
      attributesValuesDataApiSlice.middleware,
      brandsDataApiSlice.middleware,
      productDataApiSlice.middleware,
      inventoryDataApiSlice.middleware,
      dashboardDataApiSlice.middleware,
      customerDataApiSlice.middleware,
      customerContactsDataApiSlice.middleware,
      customerNotesDataApiSlice.middleware,
      customerAttachmentsDataApiSlice.middleware,
      supplierDataApiSlice.middleware,
      countriesDataApiSlice.middleware,
    ),
});
export const persistor = persistStore(store);

setupListeners(store.dispatch);
