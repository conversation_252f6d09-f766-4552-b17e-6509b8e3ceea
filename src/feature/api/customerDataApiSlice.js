import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const customerDataApiSlice = createApi({
  reducerPath: "customerDataApiSlice",
  baseQuery,
  tagTypes: ["customerData"],
  endpoints: (builder) => ({
    getCustomerList: builder.query({
      query: (body) => ({
        url: `customers/listCustomer`,
        method: "POST",
        body,
      }),
      providesTags: ["customerData"],
    }),
    listAllCustomer: builder.query({
      query: (body) => ({
        url: `customers/listAllCustomer`,
        method: "POST",
        body,
      }),
    }),
    deleteCustomer: builder.mutation({
      query: (body) => ({
        url: `customers/deleteCustomer`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["customerData"],
    }),
    createCustomer: builder.mutation({
      query: (body) => ({
        url: `customers/createCustomer`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["customerData"],
    }),
    editCustomer: builder.mutation({
      query: (body) => ({
        url: `customers/editCustomer`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["customerData"],
    }),
    singleCustomer: builder.mutation({
      query: (body) => ({
        url: `customers/singleCustomer`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetCustomerListQuery,
  useListAllCustomerQuery,
  useDeleteCustomerMutation,
  useCreateCustomerMutation,
  useEditCustomerMutation,
  useSingleCustomerMutation,
} = customerDataApiSlice;
